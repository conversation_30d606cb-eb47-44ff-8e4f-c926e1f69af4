<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
</head>
<body>
    <h1>API Connection Test</h1>
    <button onclick="testConnection()">Test Backend Connection</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8080', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<p style="color: green;">✅ Success: ${JSON.stringify(data)}</p>`;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ HTTP Error: ${response.status} ${response.statusText}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">❌ Network Error: ${error.message}</p>`;
                console.error('Connection test failed:', error);
            }
        }
    </script>
</body>
</html>
