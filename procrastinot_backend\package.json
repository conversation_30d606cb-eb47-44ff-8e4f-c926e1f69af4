{"name": "procrastinot_backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0"}, "devDependencies": {"axios": "^1.10.0", "nodemon": "^3.1.10"}}