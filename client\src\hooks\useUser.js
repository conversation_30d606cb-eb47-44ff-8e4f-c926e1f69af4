// src/hooks/useUser.js
import { useState, useEffect, useCallback } from 'react';
import { userService } from '../services/userService';
import { getUserFromToken } from '../utils/auth';

export const useUser = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);

  // Get current user from token
  const getCurrentUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const tokenUser = getUserFromToken();
      if (!tokenUser) {
        throw new Error('No valid authentication token found');
      }

      const userData = await userService.getCurrentUser();
      setUser(userData);
      
      // Fetch user stats
      const userStats = await userService.getUserStats(userData._id);
      setStats(userStats);
      
    } catch (err) {
      setError(err.message);
      setUser(null);
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, []);

  // Update user profile
  const updateProfile = useCallback(async (profileData) => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const updatedUser = await userService.updateProfile(user._id, profileData);
      setUser(updatedUser.user || updatedUser);
      
      return updatedUser;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Upload profile picture
  const uploadProfilePicture = useCallback(async (file) => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await userService.uploadProfilePicture(user._id, file);
      
      // Update user data with new profile picture
      setUser(prev => ({
        ...prev,
        profilePicture: result.profilePicture
      }));
      
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Change password
  const changePassword = useCallback(async (passwordData) => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await userService.changePassword(user._id, passwordData);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Refresh user data
  const refreshUser = useCallback(() => {
    getCurrentUser();
  }, [getCurrentUser]);

  // Load user on mount
  useEffect(() => {
    getCurrentUser();
  }, [getCurrentUser]);

  return {
    user,
    stats,
    loading,
    error,
    updateProfile,
    uploadProfilePicture,
    changePassword,
    refreshUser,
    isAuthenticated: !!user,
  };
};

export default useUser;
